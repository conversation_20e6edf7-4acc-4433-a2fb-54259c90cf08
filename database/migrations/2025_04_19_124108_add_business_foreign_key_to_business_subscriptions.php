<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('business_subscriptions', function (Blueprint $table) {
            // Add the foreign key constraints now that referenced tables exist
            if (Schema::hasTable('businesses')) {
                $table->foreign('business_uuid')
                      ->references('business_uuid')
                      ->on('businesses')
                      ->onDelete('cascade');
            }

            if (Schema::hasTable('plans')) {
                $table->foreign('plan_id')
                      ->references('id')
                      ->on('plans')
                      ->onDelete('cascade');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('business_subscriptions', function (Blueprint $table) {
            // Drop the foreign key constraints
            $table->dropForeign(['business_uuid']);
            $table->dropForeign(['plan_id']);
        });
    }
};
